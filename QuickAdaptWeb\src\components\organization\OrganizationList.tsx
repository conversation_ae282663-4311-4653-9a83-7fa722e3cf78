import React, { useState, useEffect } from "react"
import GetAppTwoToneIcon from '@mui/icons-material/GetAppTwoTone';
import Switch from '@mui/material/Switch';
import { format } from "date-fns";
import moment from 'moment';
import 'moment-timezone';
import styles from "./OrganizationStyles.module.scss"
import { useNavigate } from "react-router-dom";
import { <PERSON>nackbar, Typography } from '@mui/material';
// import { GridColDef, GridRenderCellParams, GridValueGetterParams } from '@mui/x-data-grid';
import {
	DataGrid,
	GridColDef,
	GridColumnMenuProps,
	GridRenderCellParams,
	GridPaginationModel,
	GridSortModel

} from "@mui/x-data-grid"
import { Button, Menu, MenuItem, FormControlLabel, IconButton, Tooltip, FormGroup, Select, Box, Alert } from "@mui/material"
import EditIcon from "@mui/icons-material/Edit"
import DeleteIcon from "@mui/icons-material/Delete"
import MailIcon from "@mui/icons-material/Mail"
import SaveAltIcon from "@mui/icons-material/SaveAlt"
import loader from "../../assets/loader.gif"
import { organizationsList } from "./orgData"
import CustomGrid from "../common/Grid"
import axios from "axios";
//import EditOrganization from "./OrganizationEdit";

import EditOrganization from "./OrganizationEdit";
import { User } from "../../models/User";
import { Organization } from "../../models/Organization";
import { activateOrganization, createOrganization, deactivateOrganization, fetchOrganizations, getOrganizations, getOrganizationsData,updateOrganization } from "../../services/OrganizationService";

import { AnyAaaaRecord } from "dns";
import { timezones } from "../../timezones";
import { fetchUserDataFromApi } from "../../services/UserService";
import { formatDateTime } from "../common/TimeZoneConversion";
import OrganizationCustomColumnMenu from "./OrganizationCustomColumnMenu";
import WarningAmberIcon from '@mui/icons-material/WarningAmber';
import CorporateFareIcon from '@mui/icons-material/CorporateFare';
import CreditScoreIcon from '@mui/icons-material/CreditScore';
import EditSubscription from "./EditSubscription";
// interface State extends SnackbarOrigin {
// 	open: boolean;
//   }

interface CustomDataGridProps extends React.ComponentProps<typeof DataGrid> {
	components?: {
		columnMenu?: React.ComponentType<GridColumnMenuProps>;
	};
}
const OrganizationList: React.FC = () => {
	const [menuVisible, setMenuVisible] = useState(false);
	const [searchText, setSearchText] = useState("");
	const [gridHeight, setGridHeight] = useState(500);
	const [models, setModels] = useState<Organization[]>([])
	const [loading, setLoading] = useState(true)
	const [activePlan, setActivePlan] = useState("")
	const [idDelete, setIdDelete] = useState("");
	const [orgIdEdit, setOrgIdEdit] = useState("")
	const [logoFile, setLogoFile] = useState<File | null>(null);
	const [organizationName, setOrganizationName] = useState('');
	const [orgNameError, setOrgNameError] = useState('');
	const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
	const [logoError, setLogoError] = useState('');
	const [inputs, setInputs] = useState({}) //useState<DataModel>() // //
	const [organizationId, setOrganizationId] = useState<number | null>(null);
	const [activeId, setActiveId] = useState<boolean | null>(null);
	const [filteredUsers, setFilteredUsers] = useState<User[]>([]);
	const [users, setUsers] = useState<User[]>([]);
	const [filters, setFilters] = useState([]);
	const [orderByFields, setOrderByFields] = useState("");
	const [isRTL, setIsRTL] = useState(false);
	const [dateFormat, setDateFormat] = useState("dd-MM-yyyy");
	const [type, setType] = useState('Client');
	const [logo, setLogo] = useState(null);
	const [logoUrl, setLogoUrl] = useState<string | null>(null);

	const [timezoneError, setTimezoneError] = useState('');
	const [dateFormatError, setDateFormatError] = useState('');
	const [currentDate, setCurrentDate] = useState("");
	const [typeError, setTypeError] = useState('');
	const [selectedTimezone, setSelectedTimezone] = useState<string>("Asia/Kolkata"); // default value
	const [switchStates, setSwitchStates] = useState<{ [key: string]: boolean }>({});
	const [snackbarOpen, setSnackbarOpen] = useState(false);
	const [snackbarMessage, setSnackbarMessage] = useState("");
	const [snackbarSeverity, setSnackbarSeverity] = useState<"success" | "error">("success");
	const [skip, setskip] = useState(0);
	const [top, settop] = useState(15);
	const [totalcount, setTotalcount] = useState(0);
	const [paginationModel, setPaginationModel] = useState<GridPaginationModel>({
		page: 0,
		pageSize: 15,
	});
	const [isPaginationResetDone, setIsPaginationResetDone] = useState(false);
	const [planType, setPlanType] = useState('Free Trail');
	const [planTypeError, setPlanTypeError] = useState("");

	const [createddate, setCreatedDate] = useState<string>("")
	const paginationReset = (isFilter : boolean) => {
		setPaginationModel( (prev) => ({ ...prev, page: 0 }));
		setIsPaginationResetDone(isFilter);
	}


	const handleTimezoneChange = (event: any) => {
		setSelectedTimezone(event.target.value);
	};
	const handleSnackbarClose = () => {
		setSnackbarOpen(false);
	};
	const [pendingSwitchState, setPendingSwitchState] = useState<boolean | null>(null);
	const [checkedOne, setCheckedOne] = useState(true)
	const [IsActive, setIsActive] = useState<boolean>(true);
	const MatEdit = (number: any) => {

		const handleMailClick = (type: any) => {

		};
		const handleDeleteClick = (organizationid: string) => {
			setShowDeletePopup(true);
			setIdDelete(organizationid);
		};


		const handleSwitchClick = (organizationid: string, currentStatus: boolean) => {
			setShowActivatePopup(false);
			setShowDeactivatePopup(false);
			setIdDelete(organizationid);
			setPendingSwitchState(!currentStatus);

			if (!currentStatus) {
				setShowActivatePopup(true);
			} else {
				setShowDeactivatePopup(true);
			}
		};
		const handleeditclick = (organizationid: string) => {
			setShowEditPopup(true);
			setOrgIdEdit(organizationid);
		};
		const handleClose = () => {
			setAnchorEl(null);
			setShowDeletePopup(false);
		};
		return (
			<div>
				<FormControlLabel
					control={
						<Tooltip title="Mail">
							<IconButton
              className="qadpt-grdicon"
								aria-label="mail"
								onClick={() => handleMailClick("mail")}
							>
								{/* Your icon component */}
							</IconButton>
						</Tooltip>
					}
					label={""}
				/>
				<FormControlLabel
					control={
						<Tooltip title="Edit Organization">
							<IconButton
              className="qadpt-grdicon"
								aria-label="edit"
								onClick={() => handleeditclick(number.organizationid)}
							>
              <EditIcon/>
							</IconButton>
						</Tooltip>
					}
					label={""}
				/>
				<FormControlLabel
					control={
						<Tooltip title="Edit Subscirption">
							<IconButton
              className="qadpt-grdicon"
								aria-label="edit"
								onClick={() => openSubscribe(number)}
							>
              <CreditScoreIcon/>
							</IconButton>
						</Tooltip>
					}
					label={""}
				/>

				<FormControlLabel
					control={

						<Switch
							checked={switchStates[number.organizationid] || false}
							onChange={() => handleSwitchClick(number.organizationid, switchStates[number.organizationid])}
							color="primary"
						/>

					}
					label=""
				/>
				

			</div>
		);
	};

	const handleChangeDate = (event: any) => {
		const newFormat = event.target.value;
		setDateFormat(newFormat);
		updateCurrentDate(newFormat);
	};
	const handleSearch = (value: string[]) => {
		setSearchText(value.join(" "));
	};
	const columns: GridColDef[] = [

			{ field: "Name", headerName: "Organization Name", width: 200,flex:1,filterable:false },
			//{ field: "DateFormat", headerName: "Date Format", width: 200 ,flex:1,disableColumnMenu: true,sortable:false},
			{ field: "Plan", headerName: "Organization Plan", width: 200,flex:1,filterable:false, disableColumnMenu: true},
		{
			field: "CreatedDate",
			headerName: "Created Date",
			width: 200,
			flex: 1,
			filterable: false,
			sortable: true,
			disableColumnMenu: true,
			renderCell: (params) => {
				const dateString = params.row.CreatedDate;
				const formatStr = params.row.DateFormat as
					'dd-MM-yyyy' |
					'MM-dd-yyyy' |
					'yyyy-MM-dd' |
					'dd/mm/yyyy' |
					'mm/dd/yyyy' |
					'yyyy/mm/dd';
				return formatDateTime(dateString, formatStr);
			},
		},


			{ field: "Type", headerName: "Type", width: 200,flex:1,filterable:false},
		{
			field: "actions",
			headerName: "Actions",
			sortable: false,
			disableColumnMenu: true,
			width: 300,
			flex: 1,
			renderCell: (params) => {
				const organizationid = params.row.OrganizationId || false;
				setOrganizationId(organizationid);
				const date = params.row.CreatedDate || false;
				setCreatedDate(date);



				return (
			  <div>
						<MatEdit

							organizationid={organizationid}
							index={params.row.id}
						/>
					</div>
				);
			},
		},
	];

	const [selectedValue, setSelectedValue] = useState("option1")

	const handleRadioChange = (value: any) => {
		setSelectedValue(value)
	}
	const onPageChange = (newPage: number) => {
	}
	const onPageSizeChange = (newPageSize: number) => {
	}
	const planEditClick = (type: any) => {
		setActivePlan(type)
		setShowSubscribe(true)
	}
	const [organizations, setOrganizations] = useState<Organization[]>([]);
	const [sortModel, setSortModel] = useState<GridSortModel>([{ field: 'Name', sort: 'asc' }]); 
	const [filterModel, setFilterModel] = useState({ items: [] });
	const setModelsandOptions = (modelOptions: any) => {
		setModels(modelOptions);
		setOptionsModel(modelOptions)
	}
	useEffect(() => {
		const fetchData = async () => {
			const skipcount = paginationModel.pageSize || 10;
			const limitcount = paginationModel.page * skipcount;
			const skips = limitcount;
			const top = paginationModel.pageSize;
			setskip(skips);
			settop(top);
			if (filters.length > 0) {
				await getOrganizations(setModels, setLoading, skips, top, setTotalcount, sortModel, filters);
			} else {
				await getOrganizations(setModelsandOptions, setLoading, skips, top, setTotalcount, sortModel, filters);
			}
		};
		if (isPaginationResetDone) {
			setIsPaginationResetDone(false);//Here to skip the effect after paginationReset it has been, called
		} else {
			fetchData();
		}
		//setOptionsModel(models);
	}, [paginationModel]);
	
	useEffect(() => {
		const fetchData = async () => {
			
			const skipcount = paginationModel.pageSize || 10;
			const limitcount = paginationModel.page * skipcount;
			const skips = limitcount;
			const top = paginationModel.pageSize;
			setskip(skips);
			settop(top);
			
			if (filters.length > 0) {
				await getOrganizations(setModels, setLoading, skips, top, setTotalcount, sortModel, filters);
			} else {
				await getOrganizations(setModelsandOptions, setLoading, skips, top, setTotalcount, sortModel, filters);
			}
		};
		fetchData();
	},[sortModel]);

	useEffect(() => {
		
		paginationReset(filters.length>0)
		
	}, [filters]);

	const [modelsData, setModelsData] = useState<Organization[]>([])

	useEffect(() => {
		const fetchAllData = async () => {
			try {
				await fetchOrganizations(setModelsData, setLoading);
			} catch (error) {
			} finally {
				setLoading(false);
			}
		};

		fetchAllData();
	}, []);

	const handleSortModelChange = (model: any) => {
		setSortModel(model);
		setLoading(false);
	};
	const filteredRows = models.filter((row: any) => {
		const emailId = row.EmailId || "";
		const contactnumber = row.ContactNumber || "";
		const userName = row.UserName || "";

		return (
			emailId.toLowerCase().includes(searchText.toLowerCase()) ||
			contactnumber.toLowerCase().includes(searchText.toLowerCase()) ||
			userName.toLowerCase().includes(searchText.toLowerCase())
		);
	});
	useEffect(() => {
		setSwitchStates(models.reduce((acc, org) => {
			acc[org.OrganizationId] = org.IsActive;
			return acc;
		}, {} as { [key: string]: boolean }));
	}, [models])
	const [showPopup, setShowPopup] = useState(false)
	const [showeditPopup, setShowEditPopup] = useState(false);
	const [showSubscribe, setShowSubscribe] = useState(false)
	const [showDeletePopup, setShowDeletePopup] = useState(false);
	const [showDeactivatePopup, setShowDeactivatePopup] = useState(false);
	const [showActivatePopup, setShowActivatePopup] = useState(false);
	const [optionsModel, setOptionsModel] = useState<Organization[]>([]);
	const navigate = useNavigate();
	const openPopup = () => {
		setShowPopup(true)
	}
	const openSubscribe = (number:any) => {
		setOrgIdEdit(number.organizationid);
		setShowSubscribe(true);
	}
	const closeSubscribe = () => {
		setShowSubscribe(false)
	}
	const CustomToolbar: React.FC<any> = () => {
		const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null)

		const handleExportMenuClick = (event: React.MouseEvent<HTMLButtonElement>) => {
			setAnchorEl(event.currentTarget)
		}

		const handleExportMenuClose = () => {
			setAnchorEl(null)
		}

		const handleDownloadExcelClick = () => {
			handleExportMenuClose()
		}


		return (
			<div>

				{/* <Button
					aria-controls="export-menu"
					aria-haspopup="true"
					onClick={handleExportMenuClick}
					style={{ marginLeft: "10px" }}
					startIcon={<SaveAltIcon />}
				>
					Export
				</Button> */}
				{/* <Menu
					id="export-menu"
					anchorEl={anchorEl}
					keepMounted
					open={Boolean(anchorEl)}
					onClose={handleExportMenuClose}
				>
					<MenuItem
						onClick={() => {
							handleDownloadExcel()
							handleExportMenuClose()
						}}
					>
						Download Excel
					</MenuItem>
				</Menu> */}
			</div>
		)
	}


	const handleSwitchChange = (organizationid: any, newStatus: boolean) => {
		setSwitchStates(prevStates => ({
			...prevStates,
			[organizationid]: newStatus
		}));
	};

	const handleActivateClick = async () => {
		if (idDelete !== null) {
			await activateOrganization(idDelete, checkedOne, setShowActivatePopup, setModels, setLoading);
			handleSwitchChange(idDelete, true); 
			await getOrganizations(setModels, setLoading, skip, top, setTotalcount, sortModel, filters);

			setShowActivatePopup(false);
			setSnackbarMessage("Organization Activated successfully");
			setSnackbarSeverity("success");
			setSnackbarOpen(true);
			setTimeout(() => {
				setSnackbarOpen(false);
			}, 2000);
		}
	};

	const handleDeactivateClick = async () => {
		if (idDelete !== null) {
			await deactivateOrganization(idDelete, checkedOne, setShowDeactivatePopup, setModels, setLoading);
			handleSwitchChange(idDelete, false); 
			await getOrganizations(setModels, setLoading, skip, top, setTotalcount, sortModel, filters);

			setShowDeactivatePopup(false);
			setSnackbarMessage("Organization Deactivated successfully");
			setSnackbarSeverity("success");
			setSnackbarOpen(true);
			setTimeout(() => {
				setSnackbarOpen(false);
			}, 2000);
		}
	};
	let logoFileName = '';
	const handleChange = (e: any) => {
		const { name, value, checked } = e.target;
		switch (name) {
			case 'Name':
				setOrganizationName(value);
				validateOrganizationName(value)
				break;
			case 'isRTL':
				setIsRTL(checked);
				break;
			case 'Type':
				setType(value);
				break;
			case 'Plan Type':
				setPlanType(value);
				break;

			default:
				break;
		}
	};


	const updateCurrentDate = (format: any) => {
		const date = new Date();
		let formattedDate = "";

		switch (format) {
			case "yyyy-MM-dd":
				formattedDate = date.toISOString().split("T")[0];
				break;
			case "dd-MM-yyyy":
				formattedDate = `${String(date.getDate()).padStart(2, "0")}-${String(date.getMonth() + 1).padStart(2, "0")}-${date.getFullYear()}`;
				break;
			case "MM-dd-yyyy":
				formattedDate = `${String(date.getMonth() + 1).padStart(2, "0")}-${String(date.getDate()).padStart(2, "0")}-${date.getFullYear()}`;
				break;
			case "dd/mm/yyyy":
				formattedDate = `${String(date.getDate()).padStart(2, "0")}/${String(date.getMonth() + 1).padStart(2, "0")}/${date.getFullYear()}`;
				break;
			case "mm/dd/yyyy":
				formattedDate = `${String(date.getMonth() + 1).padStart(2, "0")}/${String(date.getDate()).padStart(2, "0")}/${date.getFullYear()}`;
				break;
			case "yyyy/mm/dd":
				formattedDate = `${date.getFullYear()}/${String(date.getMonth() + 1).padStart(2, "0")}/${String(date.getDate()).padStart(2, "0")}`;
				break;
			default:
				formattedDate = date.toISOString().split("T")[0];
		}

		setCurrentDate(formattedDate);
	};


	useEffect(() => {
		updateCurrentDate(dateFormat);
	}, []);
	const validateOrganizationName = (name: string) => {
		setOrgNameError('');
		const trimmedName = name.trim();
		if (trimmedName === '') {
			setOrgNameError('Organization Name is required');
			return;
		}
		if ((trimmedName.length < 5 || trimmedName.length > 50) && (!/^[a-zA-Z0-9 ]+$/.test(trimmedName))) {
			setOrgNameError('Organization Name must be between 5 and 50 characters.Special characters are not allowed');
			return;
		}
		if (trimmedName.length < 5 || trimmedName.length > 50) {
			setOrgNameError('Organization Name must be between 5 and 50 characters');
			return;
		}
		if (!/^[a-zA-Z0-9 ]+$/.test(trimmedName)) {
			setOrgNameError('Special characters are not allowed');
			return;
		}
		const isDuplicateName = modelsData.some(org => org.Name === trimmedName);
		if (isDuplicateName) {
			setOrgNameError('Organization Name already exists');
			return;
		}
		setOrgNameError('');
	};



	const handleClose = () => {
		setAnchorEl(null);
		setShowDeletePopup(false);
		setOrganizationName("");
		setLogoFile(null);
		setShowPopup(false);
		setShowEditPopup(false);
		setShowSubscribe(false);
		setOrgNameError('');
		setLogoError("");
	};

	const toBase64 = (file: File): Promise<string> => {
		return new Promise((resolve, reject) => {
			const reader = new FileReader();
			reader.readAsDataURL(file);
			reader.onload = () => resolve(reader.result as string);
			reader.onerror = error => reject(error);
		});
	};
	const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
		if (event.target.files && event.target.files[0]) {
			const file = event.target.files[0];
			setLogoFile(file);
			setLogoUrl(URL.createObjectURL(file));
			logoFileName = file.name;
			setLogoError('')
		}
	};

	const handleSubmitOrganization = async (event: any) => {
		event.preventDefault();
		setOrgNameError('');
		setDateFormatError('');
		setTypeError('');
		setLogoError('');
		let hasError = false;
		const trimmedNameNew = organizationName.trim();
		validateOrganizationName(trimmedNameNew);

		if (orgNameError) {
			hasError = true;
		}

		// if (!dateFormat) {
		// 	setDateFormatError('Date format is required.');
		// 	hasError = true;
		// }

		if (!type) {
			setTypeError('Type is required.');
			hasError = true;
		}

		if (!planType) {
			setPlanType("Plan is Requried");
			hasError = true;
		}

		// if (!logoFile) {
		// 	setLogoError('Logo is required');
		// 	hasError = true;
		// } else {
		// 	setLogoError('');
		// 	logoFileName = logoFile.name; 
		// }

		if (hasError) {
			return;
		}
		const reqObj = {
			Name: organizationName,
			Logo: logoFileName, 
			TimeZone: selectedTimezone,
			DateFormat: dateFormat,
			Type: type,
			RTL: isRTL,
			Plan: planType,
		};
		createOrganization(
			setLoading,
			setShowPopup,
			setModels,
			setShowEditPopup,
			reqObj,
			setSnackbarMessage,
			setSnackbarSeverity,
			setSnackbarOpen,
			(organizationId: string) => {
				setOrgNameError('');
				setOrganizationName('');
				setLogoFile(null);
				setLogoError('');
				const redirectUrl = `/superadmin/${organizationId}/createadmin`;
				setTimeout(() => {
					navigate(redirectUrl);
				}, 3000);
			}
		);
	};

	const updateOrganizationDetails = async (orgDetails:any)=>{
		await updateOrganization(
			setLoading,
			setModels,
			setShowEditPopup,
			orgDetails,
			setSnackbarMessage,
			setSnackbarSeverity,
			setSnackbarOpen,
			() => {
			  setShowEditPopup(false);
			  getOrganizations(setModelsandOptions, setLoading, skip, top, setTotalcount, sortModel, []);
			  fetchOrganizations(setModelsData, setLoading);
			}
		);
		setShowSubscribe(false);
		setShowEditPopup(false);
	}

	const validateLogo = () => {
		if (!logoFile) {
			setLogoError('Logo is required');
		} else {
			setLogoError('');
		}
	};
	const [columnMenuApi, setColumnMenuApi] = useState<any>(null); // Store the API instance

	const handleColumnMenuClose = () => {
		if (columnMenuApi) {
			columnMenuApi.hideColumnMenu(); // Close the column menu
		}
	};
	const isFormValid = !orgNameError ;
	return (
		<div>
			<div className="qadpt-head">
						<div className="qadpt-title-sec">
							<div className="qadpt-title">Organization List</div>
						</div>
						<div className="qadpt-right-part">
				<button
					onClick={openPopup}
								className="qadpt-memberButton"
				>
								<i className="fal fa-add-plus"></i>
								<span>Create Organization</span>
				</button>
			</div>
						
					</div>

			{loading ? (
				 <div className="Loaderstyles">
				 <img
				 src={loader}
					   alt="Spinner"
					   className="LoaderSpinnerStyles"
				 />
			  </div>
			) : (
					<div>

				<DataGrid className="qadpt-org-grd"
//   sx={{
//     borderColor: "black",
//     "& .MuiDataGrid-columnHeaders": {
//       backgroundImage: "linear-gradient(to right, rgb(30, 138, 201), rgba(62, 200, 241, 0.7))",
//     },
//     "& .MuiDataGrid-columnHeaderTitle": {
//       fontWeight: "bold",
//       color: "white"
//     },
//     "& .MuiDataGrid-columnHeader": {
//       backgroundImage: "linear-gradient(to right, rgb(30, 138, 201), rgba(62, 200, 241, 0.7))",
//       borderBottom: "2px solid #ddd",
//       color: "white",
//     },
//     "& .MuiDataGrid-columnHeader--alignLeft": {
//       backgroundImage: "linear-gradient(to right, rgb(30, 138, 201), rgba(62, 200, 241, 0.7))",
//       color: "white",
//     },
//     "& .MuiDataGrid-cell": {
//       borderBottom: "2px solid #ddd",
//       borderRight: "2px solid #ddd",
//     },
//     "& .MuiDataGrid-columnHeader, .MuiDataGrid-cell": {
//       borderRight: "2px solid #ddd",
//     },
//     "& .MuiDataGrid-row": {
//       "&:last-child .MuiDataGrid-cell": {
//         borderBottom: "none",
//       },
//     },
//     "& .MuiDataGrid-menuIconButton": {
//       visibility: "visible",
//       opacity: 1,
//     },
//   }}
						rows={models}
						columns={columns}
						getRowId={(row) => row.OrganizationId}
						paginationModel={paginationModel}
						onPaginationModelChange={(model) => {
							setPaginationModel(model);
							setLoading(false);
						}}
						pagination
						paginationMode="server"
						rowCount={totalcount}
						pageSizeOptions={[15, 25, 50, 100]}
						localeText={{
							MuiTablePagination: {
								labelRowsPerPage: "Records Per Page",
							},
						}}
						disableColumnSelector
						loading={loading}
						disableRowSelectionOnClick={false}
						sortModel={sortModel}
						onSortModelChange={handleSortModelChange}
						slots={{
							columnMenu: (menuProps) => {
								if (menuProps.colDef.field === "Name" || menuProps.colDef.field === "Type") {
									return (
										<OrganizationCustomColumnMenu
											key={modelsData.map(m => m.OrganizationId).join(",")}
											column={menuProps.colDef.field}
											setModels={setModels}
											setLoading={setLoading}
											skip={skip}
											top={top}
											OrganizationId={organizationId}
											sortModel={sortModel}
											setTotalcount={setTotalcount}
											orderByFields={orderByFields}
											filters={filters}
											models={models}
											modelsData={modelsData}
											setFilters={setFilters}
											{...menuProps}
											optionsModel={optionsModel}
											options={modelsData.map((model: any) => model[menuProps.colDef.field] || "")}
											onSearch={handleSearch}
											hideMenu={menuProps.hideMenu}
											paginationModel={paginationModel}
										/>
									);
								}
								return null;
							},
						}}

					/>


				</div>
			)}



			{showPopup ? (
				<div className="user-popup">
					<div className='qadpt-header'>
						<span>Create Organization</span>
						<svg
							onClick={() => handleClose()}
							className="close-icon"
							xmlns="http://www.w3.org/2000/svg"
							x="0px"
							y="0px"
							width="24"
							height="24"
							viewBox="0 0 50 50"
						>
							<path d="M 7.71875 6.28125 L 6.28125 7.71875 L 23.5625 25 L 6.28125 42.28125 L 7.71875 43.71875 L 25 26.4375 L 42.28125 43.71875 L 43.71875 42.28125 L 26.4375 25 L 43.71875 7.71875 L 42.28125 6.28125 L 25 23.5625 Z"></path>
						</svg>
					</div>
					<div className="qadpt-usrform">
						<form onSubmit={handleSubmitOrganization}>
							<div className="qadpt-txtfld">
					<label htmlFor="organizationname" className="qadpt-txtlabel">Organization Name*</label>
						<input
					className={`qadpt-txtinp ${orgNameError ? "error-input" : ""}`}
					type="text"
							name="Name"
							value={organizationName}
									onChange={handleChange}
						// onFocus={() => validateOrganizationName(organizationName)}
						></input>
						{orgNameError && <span className="error">{orgNameError}</span>}
						</div>
					
						{/* <div className="qadpt-txtfld">
						<label htmlFor="logo" className="qadpt-txtlabel"> Logo*</label>
						<input
							className={logoError ? "error-input" : "qadpt-txtinp"}
							type="file"
							name="logo"
							accept="image/*"
							onChange={handleFileChange} */}
						{/* // onFocus={validateLogo} // Validate on focus */}
						{/* ></input> */}
						{/* {logoError && <span className="error">{logoError}</span>} */}



						{/* {logoUrl && (
                <div>
                    <img src={logoUrl} alt="Logo" style={{ marginTop: 10, maxWidth: '200px', maxHeight: '200px' }} />
                </div>
            )} */}
						{logoError && <span className="error">{logoError}</span>}
						{/* </div> */}
					{/* <div className="qadpt-txtfld">
					
					<label htmlFor="timezone" className="qadpt-txtlabel">Timezone*</label>

						<select

							value={selectedTimezone}
									onChange={handleTimezoneChange}
									className="qadpt-txtinp"

						>
							{timezones.map((timezone: any) => (
								<option key={timezone.Id} value={timezone.Id}>
									{timezone.DisplayName}
								</option>
							))}
						</select>
						{timezoneError && <span className="error">{timezoneError}</span>}
					</div> */}
					{/* <div>
					  <label className='class-labels'>RTL</label>
          <FormGroup>
            <FormControlLabel control={<Switch />} label="disabled" />
          </FormGroup>
        </div> */}

							
					{/* <div className="qadpt-txtfld">
					<label htmlFor="dateFormat" className="qadpt-txtlabel">Date Format*</label>
						<select
							id="dateFormat"
							name="DateFormat"
							value={dateFormat}
							onChange={handleChangeDate}
							className={dateFormatError ? "error-input" : "qadpt-txtinp"}
						>
							<option value="dd-MM-yyyy">dd-MM-yyyy</option>
							<option value="MM-dd-yyyy">MM-dd-yyyy</option>
							<option value="yyyy-MM-dd">yyyy-MM-dd</option>
							<option value="dd/mm/yyyy">dd/mm/yyyy</option>
							<option value="mm/dd/yyyy">mm/dd/yyyy</option>
							<option value="yyyy/mm/dd">yyyy/mm/dd</option>
							Add more options as needed
						</select>

						{dateFormatError && <span className="error">{dateFormatError}</span>}
					</div> */}

					
					<div className="qadpt-txtfld">
          <label htmlFor="type" className="qadpt-txtlabel">Type* </label>
						<select
							id="type"
							name="Type"
							value={type}
							onChange={handleChange}
							className={typeError ? "error-input" : "qadpt-txtinp"}
						>
							<option value="Client">Client</option>
							<option value="Testing">Testing</option>
							<option value="POC">POC</option>
							<option value="Prospects">Prospects</option>
						</select>
						{typeError && <span className="error">{typeError}</span>}
							</div>
							<div className="qadpt-txtfld">
          <label htmlFor="type" className="qadpt-txtlabel">Plan </label>
						<select
							id="plan"
							name="Plan Type"
							value={planType}
							onChange={handleChange}
							className={planTypeError ? "error-input" : "qadpt-txtinp"}
						>
							<option value="Free Trail">Free Trail</option>
							<option value="Basic">Basic</option>
							<option value="Advanced">Advanced</option>
						</select>
						{planTypeError && <span className="error">{planTypeError}</span>}
					</div>

					{/* <div style={{ marginTop: "14px", marginBottom: "9px" }}>
							<span style={{ marginRight: "90px", marginTop: "5px" }}>RTL</span>
							<label className="switch">
							<input type="checkbox"  />
								<span className="slider round" style={{height:27,width:53}}></span>
							</label>
						</div>  */}
					 <div className="qadpt-txtfld qadpt-switch">
						<span  className="qadpt-txtlabel">RTL </span>
						<FormControlLabel
					control={

						<Switch
								
								id="isRTL"
								name="isRTL"
								checked={isRTL}
								onChange={handleChange}
							/>
					}
					label=""
/>
					

				
					</div>
					<div>
						</div>
						</form>
						</div>
					<div className="qadpt-button">
					<button
							onClick={handleSubmitOrganization}
							className={isFormValid ? "qadpt-enab" : "qadpt-disab"}
							disabled={!isFormValid}
						>
							Save
						</button>
						</div>
				</div>
			) : (
				""
			)}

			{showeditPopup ? (
				<EditOrganization
					modelsData={modelsData}
					setModelsData={setModelsData}
					showEditPopup={showeditPopup}
					setShowEditPopup={setShowEditPopup}
					OrganizationId={orgIdEdit}
					sortModel={sortModel}
					filters={filters}

					getOrganizations={getOrganizations}
					setModels={setModels}
					models={models}
					setLoading={setLoading}
					handleClose={handleClose}
					skip={skip}
					top={top}
					setTotalcount={setTotalcount}
					updateOrganizationDetails={updateOrganizationDetails}
				/>
			) : (
				""
			)}

			{showActivatePopup ? (
                 <div className="qadpt-modal-overlay">
				 <div className="qadpt-usrconfirm-popup qadpt-success">
						  <div>
			   <div className="qadpt-icon">
				 <CorporateFareIcon/>
							  </div>
							  </div>
			   <div className="qadpt-popup-title">Activate Account</div>
			   <div className="qadpt-warning">
							Are you sure you want to Activate Organization
						</div>
			   <div className="qadpt-buttons">
				 <button
				   onClick={() => setShowActivatePopup(false)}
				   className="qadpt-cancel-button"
				 >
				   Cancel
				 </button>
				 <button
				   onClick={handleActivateClick}
				   className="qadpt-conform-button"
				   type="submit"
				 >
				   Activate
				 </button>
			   </div>
			 </div>
		   </div>
			) : (
				""
			)}


			{showDeactivatePopup ? (
           <div className="qadpt-modal-overlay">
					<div className="qadpt-usrconfirm-popup qadpt-danger">
						<div>
			 <div className="qadpt-icon">
			   <WarningAmberIcon/>
							</div>
							</div>
			 <div className="qadpt-popup-title">Deactivate Account</div>
			 <div className="qadpt-warning">
			   Are you sure you want to deactivate this organization? This action cannot be undone.
			 </div>
			 <div className="qadpt-buttons">
			   <button
				 onClick={() => setShowDeactivatePopup(false)}
				 className="qadpt-cancel-button"
			   >
				 Cancel
			   </button>
			   <button
				 onClick={handleDeactivateClick}
				 className="qadpt-conform-button"
				 type="submit"
			   >
				 Deactivate
			   </button>
			 </div>
		   </div>
		 </div>
		 
		   
			) : (
				""
			)}



			<Snackbar
				open={snackbarOpen}
				autoHideDuration={6000}
				onClose={handleSnackbarClose}
				anchorOrigin={{ vertical: "top", horizontal: "center" }}
				sx={{ zIndex: 10000, marginTop: 4 }} // Optionally adjust the zIndex if needed
			>
				<Alert
					onClose={handleSnackbarClose}
					severity={snackbarSeverity}
					sx={{ width: "100%" }}
				>
					{snackbarMessage}
				</Alert>
			</Snackbar>












			{showSubscribe ? (
				<EditSubscription
					closeSubscribe={closeSubscribe}
					showSubscribe={showSubscribe}
					organizationId={orgIdEdit}
					setSnackbarOpen={setSnackbarOpen}
					setSnackbarSeverity={setSnackbarSeverity}
					setSnackbarMessage={setSnackbarMessage}
					updateOrganizationDetails={updateOrganizationDetails}
					
				/>
			) : (
				""
			)}
		</div>
	)
}

export default OrganizationList