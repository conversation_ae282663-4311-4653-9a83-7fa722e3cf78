import React, { Suspense, lazy,useEffect,useState } from "react";
import { <PERSON>rowser<PERSON>outer as Router } from "react-router-dom";
import "./App.scss";
import CircularIndeterminate from "./components/common/CircularIndeterminate";
import Layout from "./components/layout/Layout";
import { RtlProvider } from "./RtlContext";
import { SnackbarProvider } from "./SnackbarContext";
import Routing from "./routing/Routings";
import { AuthProvider, useAuth } from "./components/auth/AuthProvider";
import { useLocation,useNavigate } from "react-router-dom";
import { useParams } from "react-router-dom";
import { ResetPasswordLinkConsumed } from "./services/ExpirelinkService";
import { setupInterceptors } from "./services/APIService";
import { AccountProvider } from "./components/account/AccountContext";
import { ExtensionProvider } from "./ExtensionContext";
import Cookies from "js-cookie";
import { User } from "./models/User";
import jwtDecode from "jwt-decode";

function App() {
	const location = useLocation();
		//const passwordLogId = useParams();
  const navigate = useNavigate();
  setupInterceptors(navigate, (message: string, severity = 'info') => {
    // console.log(`[${severity.toUpperCase()}]: ${message}`);
  });
	const { signOut, loggedOut } = useAuth();
		//const history = useHistory();
  const [isResetLinkValid, setIsResetLinkValid] = useState(false);
  const [isCheckingLink, setIsCheckingLink] = useState(false);
  //const noLayoutRoutes = ["/login", "/forgotpassword", "/resetpassword/passwordLogId", "/admin/adminlogin"];
  const [loginUserDetails, setLoginUserDetail] = useState<User | null>();
  const noLayoutRoutes = ["/login", "/forgotpassword", "/resetpassword/:passwordLogId", "/uninstall","/admin/adminlogin", "/linkexpired"];
  const uuidRegex = "[0-9a-fA-F-]{36}";
  const isNoLayoutRoute = noLayoutRoutes.some(route => {
    if (route === "/resetpassword/:passwordLogId") {
      const resetPasswordRegex = new RegExp(`^/resetpassword/${uuidRegex}$`);
      return resetPasswordRegex.test(location.pathname);
    }
		//console.log(location.pathname === route)
		return location.pathname === route;
  });
  const token = localStorage.getItem("access_token");
  if (token) {
    const decodedToken: any = jwtDecode(token);
    const currentTime = Math.floor(Date.now() / 1000);
    if (decodedToken.exp < currentTime) {
      localStorage.clear();
      navigate("/login")
    }
  }
  const extractPasswordLogId = () => {
    const match = location.pathname.match(new RegExp(`/resetpassword/(${uuidRegex})`));
    return match ? match[1] : null;
  };

  const passwordLogId = extractPasswordLogId();
  const checkResetLinkConsumed = async (passwordLogId: any) => {
    try {
      setIsCheckingLink(true);
		const response = await ResetPasswordLinkConsumed(passwordLogId);

      if (response === true) {
        const isConsumed = await response
        setIsResetLinkValid(!isConsumed);
        if (isConsumed === true) {
          navigate("/linkexpired");
        }
      } else {
        navigate(`/resetpassword/${passwordLogId}`)
      }
	} catch (error) {
		//console.log('this is app.tsx and got error', error)
      	navigate("/login")
    } finally {
      setIsCheckingLink(false);
    }
	};



  useEffect(() => {
	if (location.pathname.includes("/resetpassword") && passwordLogId) {
		checkResetLinkConsumed(passwordLogId);
	}
  }, [passwordLogId]);

  // Load QuickAdopt embedded script dynamically with user details
  useEffect(() => {
    const scriptUrl = process.env.REACT_APP_EMBEDDED_SCRIPT_URL;
    const enableScript = process.env.REACT_APP_ENABLE_EMBEDDED_SCRIPT === 'true';

    if (!enableScript) {
      console.log('QuickAdopt embedded script is disabled via REACT_APP_ENABLE_EMBEDDED_SCRIPT environment variable');
      return;
    }

    if (!scriptUrl) {
      console.warn('REACT_APP_EMBEDDED_SCRIPT_URL environment variable is not set');
      return;
    }

    // QuickAdopt script with user details capture
    (function (g: any, u: any, i: any, d: any, e: any, s: any) {
      g[e] = g[e] || []
      g.AccountId = s;
      var f = u.getElementsByTagName(i)[0]
      var k = u.createElement(i) as HTMLScriptElement;
      k.async = true;
      k.src = scriptUrl;
      f.parentNode.insertBefore(k, f);
      k.onload = function() {
        if (g.captureUserDetails) {
          // Get user details from localStorage or context
          const userInfo = JSON.parse(localStorage.getItem("userInfo") || '{}');
          const userDetails = userInfo['user'] ? JSON.parse(userInfo['user']) : {};
          const orgDetails = userInfo['orgDetails'] ? JSON.parse(userInfo['orgDetails']) : {};

          g.captureUserDetails({
            OrganizationId: orgDetails.OrganizationId || '',
            UserId: userDetails.UserId || '',
            UserName: `${userDetails.FirstName || ''} ${userDetails.LastName || ''}`.trim(),
            EmailId: userDetails.EmailId || '',
            UserType: userDetails.UserType || '',
            ScreenResolution: `${window.screen.width}x${window.screen.height}`,
            SessionId: localStorage.getItem("access_token") || '',
            TimeZone: Intl.DateTimeFormat().resolvedOptions().timeZone || ''
          });
        }
      }
    })(window, document, 'script', 'guiding', 'layer', '25072025-080558004-6eaec78c-56d9-4013-ab79-aba4c21dbc44');

    return () => {
      // Cleanup: remove script when component unmounts
      const existingScript = document.querySelector('script[src="' + scriptUrl + '"]');
      if (existingScript && document.body.contains(existingScript)) {
        document.body.removeChild(existingScript);
      }
    };
  }, []); // Empty dependency array means this runs once on mount

  // Extension detection is now handled by ExtensionContext
  if (isCheckingLink) {
    return <div>Loading...</div>;
  }

  return (
	  <>
      <SnackbarProvider>
        <ExtensionProvider>
          {isNoLayoutRoute ? (
            <Routing />
          ) : (
            <AuthProvider>
              <AuthWrapper />
            </AuthProvider>
          )}
        </ExtensionProvider>
      </SnackbarProvider>
    </>
  );

}

// AuthWrapper to handle loading state
const AuthWrapper: React.FC = () => {
  const { isLoading } = useAuth();

  if (isLoading) {
    return <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
      <CircularIndeterminate />
    </div>;
  }

  return (
    <AccountProvider>
      <Suspense fallback={<div>Loading...</div>}>
        <RtlProvider>
          <Layout>
            <Routing />
          </Layout>
        </RtlProvider>
      </Suspense>
    </AccountProvider>
  );
};

export default App;
