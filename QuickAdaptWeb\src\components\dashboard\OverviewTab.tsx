import React, { useState } from 'react';
import { Box, Typography } from '@mui/material';
import { styled } from '@mui/material/styles';
import {
  TrendingUp,
  TrendingDown,
  People,
  CheckCircle,
  Star,
  Schedule,
} from '@mui/icons-material';
import Card from '../common/Card';

interface MetricCardProps {
  title: string;
  value: string;
  change: string;
  changeValue: string;
  trend: 'up' | 'down';
  icon: React.ReactNode;
  color: string;
}

const MetricsGrid = styled(Box)({
  display: 'grid',
  gridTemplateColumns: 'repeat(auto-fit, minmax(280px, 1fr))',
  gap: 'var(--spacing-4)',
  marginBottom: 'var(--spacing-6)',
});

const MetricCardContainer = styled(Card)({
  padding: 'var(--spacing-5)',
  display: 'flex',
  alignItems: 'center',
  gap: 'var(--spacing-4)',
});

const MetricIcon = styled(Box)<{ color: string }>(({ color }) => ({
  width: '48px',
  height: '48px',
  borderRadius: 'var(--radius-lg)',
  backgroundColor: `${color}15`,
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  
  '& svg': {
    color: color,
    fontSize: '24px',
  },
}));

const MetricContent = styled(Box)({
  flex: 1,
});

const MetricTitle = styled(Typography)({
  fontSize: 'var(--font-size-sm)',
  color: 'var(--color-gray-600)',
  marginBottom: 'var(--spacing-1)',
});

const MetricValue = styled(Typography)({
  fontSize: 'var(--font-size-2xl)',
  fontWeight: 'var(--font-weight-bold)',
  color: 'var(--color-gray-900)',
  marginBottom: 'var(--spacing-1)',
});

const MetricChange = styled(Box)({
  display: 'flex',
  alignItems: 'center',
  gap: 'var(--spacing-1)',
});

const ChangeIndicator = styled(Box)<{ trend: 'up' | 'down' }>(({ trend }) => ({
  display: 'flex',
  alignItems: 'center',
  gap: 'var(--spacing-1)',
  fontSize: 'var(--font-size-xs)',
  fontWeight: 'var(--font-weight-medium)',
  color: trend === 'up' ? 'var(--color-success-600)' : 'var(--color-error-600)',
  
  '& svg': {
    fontSize: '16px',
  },
}));

const MetricCard: React.FC<MetricCardProps> = ({
  title,
  value,
  change,
  changeValue,
  trend,
  icon,
  color,
}) => {
  return (
    <MetricCardContainer shadow="sm" hover>
      <MetricIcon color={color}>
        {icon}
      </MetricIcon>
      <MetricContent>
        <MetricTitle>{title}</MetricTitle>
        <MetricValue>{value}</MetricValue>
        <MetricChange>
          <ChangeIndicator trend={trend}>
            {trend === 'up' ? <TrendingUp /> : <TrendingDown />}
            {change}
          </ChangeIndicator>
          <Typography variant="caption" color="text.secondary">
            {changeValue}
          </Typography>
        </MetricChange>
      </MetricContent>
    </MetricCardContainer>
  );
};

// Interactive User Activity Chart Component
const UserActivityChart: React.FC = () => {
  const [hoveredPoint, setHoveredPoint] = useState<{week: string, type: string, value: number, x: number, y: number} | null>(null);

  const chartData = {
    weeks: ['Week 1', 'Week 2'],
    data: [
      { week: 'Week 1', active: 30000, retained: 24000, total: 36000, x: 125 },
      { week: 'Week 2', active: 31000, retained: 25000, total: 37000, x: 325 }
    ]
  };

  const getYPosition = (value: number) => {
    // Scale: 0-60000 maps to 200-20 (inverted Y axis)
    return 200 - ((value / 60000) * 180);
  };

  const handlePointHover = (week: string, type: string, value: number, x: number, y: number) => {
    setHoveredPoint({ week, type, value, x, y });
  };

  const handlePointLeave = () => {
    setHoveredPoint(null);
  };

  return (
    <Box sx={{ height: '300px', position: 'relative', backgroundColor: 'white', borderRadius: 'var(--radius-md)' }}>
      {/* Multi-line Chart */}
      <svg width="100%" height="280" viewBox="0 0 450 250" style={{ overflow: 'visible' }}>
        {/* Grid lines */}
        <defs>
          <pattern id="activityGrid" width="200" height="50" patternUnits="userSpaceOnUse">
            <path d="M 200 0 L 0 0 0 50" fill="none" stroke="#f1f5f9" strokeWidth="1"/>
          </pattern>
        </defs>
        <rect width="100%" height="200" fill="url(#activityGrid)" />

        {/* Y-axis */}
        <line x1="50" y1="20" x2="50" y2="200" stroke="#e2e8f0" strokeWidth="1"/>
        {/* X-axis */}
        <line x1="50" y1="200" x2="400" y2="200" stroke="#e2e8f0" strokeWidth="1"/>

        {/* Y-axis labels */}
        <text x="40" y="25" fontSize="11" fill="#64748b" textAnchor="end">60000</text>
        <text x="40" y="65" fontSize="11" fill="#64748b" textAnchor="end">45000</text>
        <text x="40" y="105" fontSize="11" fill="#64748b" textAnchor="end">30000</text>
        <text x="40" y="145" fontSize="11" fill="#64748b" textAnchor="end">15000</text>
        <text x="40" y="205" fontSize="11" fill="#64748b" textAnchor="end">0</text>

        {/* X-axis labels */}
        <text x="125" y="220" fontSize="11" fill="#64748b" textAnchor="middle">Week 1</text>
        <text x="325" y="220" fontSize="11" fill="#64748b" textAnchor="middle">Week 2</text>

        {/* Total Users Line (Purple) */}
        <path d={`M 125 ${getYPosition(36000)} L 325 ${getYPosition(37000)}`} fill="none" stroke="#8b5cf6" strokeWidth="3" strokeLinecap="round"/>

        {/* Active Users Line (Blue) */}
        <path d={`M 125 ${getYPosition(30000)} L 325 ${getYPosition(31000)}`} fill="none" stroke="#3b82f6" strokeWidth="3" strokeLinecap="round"/>

        {/* Retained Users Line (Green) */}
        <path d={`M 125 ${getYPosition(24000)} L 325 ${getYPosition(25000)}`} fill="none" stroke="#10b981" strokeWidth="3" strokeLinecap="round"/>

        {/* Interactive Data Points */}
        {chartData.data.map((point, index) => (
          <g key={index}>
            {/* Total Users Points */}
            <circle
              cx={point.x}
              cy={getYPosition(point.total)}
              r="6"
              fill="#8b5cf6"
              style={{ cursor: 'pointer', transition: 'all 0.2s ease' }}
              onMouseEnter={() => handlePointHover(point.week, 'Total', point.total, point.x, getYPosition(point.total))}
              onMouseLeave={handlePointLeave}
            />

            {/* Active Users Points */}
            <circle
              cx={point.x}
              cy={getYPosition(point.active)}
              r="6"
              fill="#3b82f6"
              style={{ cursor: 'pointer', transition: 'all 0.2s ease' }}
              onMouseEnter={() => handlePointHover(point.week, 'Active', point.active, point.x, getYPosition(point.active))}
              onMouseLeave={handlePointLeave}
            />

            {/* Retained Users Points */}
            <circle
              cx={point.x}
              cy={getYPosition(point.retained)}
              r="6"
              fill="#10b981"
              style={{ cursor: 'pointer', transition: 'all 0.2s ease' }}
              onMouseEnter={() => handlePointHover(point.week, 'Retained', point.retained, point.x, getYPosition(point.retained))}
              onMouseLeave={handlePointLeave}
            />
          </g>
        ))}

        {/* Hover Tooltip */}
        {hoveredPoint && (
          <g>
            {/* Tooltip Background */}
            <rect
              x={hoveredPoint.x - 35}
              y={hoveredPoint.y - 35}
              width="70"
              height="25"
              fill="white"
              stroke="#e2e8f0"
              strokeWidth="1"
              rx="4"
              style={{
                filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.1))',
                transition: 'all 0.2s ease'
              }}
            />
            {/* Tooltip Text */}
            <text
              x={hoveredPoint.x}
              y={hoveredPoint.y - 25}
              fontSize="10"
              fill="#1f2937"
              textAnchor="middle"
              fontWeight="600"
            >
              {hoveredPoint.type}: {hoveredPoint.value.toLocaleString()}
            </text>
            <text
              x={hoveredPoint.x}
              y={hoveredPoint.y - 15}
              fontSize="9"
              fill="#64748b"
              textAnchor="middle"
            >
              {hoveredPoint.week}
            </text>
          </g>
        )}
      </svg>

      {/* Legend */}
      <Box sx={{
        position: 'absolute',
        bottom: 10,
        left: '50%',
        transform: 'translateX(-50%)',
        display: 'flex',
        gap: 3,
        backgroundColor: 'rgba(255,255,255,0.9)',
        padding: '8px 16px',
        borderRadius: '8px',
        border: '1px solid #e2e8f0'
      }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Box sx={{ width: 12, height: 12, backgroundColor: '#3b82f6', borderRadius: '50%' }} />
          <Typography variant="caption" sx={{ fontSize: '11px', color: '#64748b' }}>Active</Typography>
        </Box>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Box sx={{ width: 12, height: 12, backgroundColor: '#10b981', borderRadius: '50%' }} />
          <Typography variant="caption" sx={{ fontSize: '11px', color: '#64748b' }}>Retained</Typography>
        </Box>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Box sx={{ width: 12, height: 12, backgroundColor: '#8b5cf6', borderRadius: '50%' }} />
          <Typography variant="caption" sx={{ fontSize: '11px', color: '#64748b' }}>Total</Typography>
        </Box>
      </Box>
    </Box>
  );
};

// Interactive Feature Adoption Chart Component
const FeatureAdoptionChart: React.FC = () => {
  const [hoveredSegment, setHoveredSegment] = useState<string | null>(null);

  const chartData = [
    { name: 'Tours', value: 25, actualValue: 1360, color: '#3b82f6', offset: 0 },
    { name: 'Tooltips', value: 20, actualValue: 1089, color: '#10b981', offset: 25 },
    { name: 'AI Assistant', value: 19, actualValue: 1034, color: '#8b5cf6', offset: 45 },
    { name: 'Banners', value: 18, actualValue: 980, color: '#f97316', offset: 64 },
    { name: 'Hotspots', value: 12, actualValue: 653, color: '#14b8a6', offset: 82 },
    { name: 'Checklists', value: 6, actualValue: 327, color: '#ef4444', offset: 94 }
  ];

  const radius = 60;
  const strokeWidth = 25;

  const createPath = (startAngle: number, endAngle: number, isHovered: boolean) => {
    const centerX = 90;
    const centerY = 90;
    const outerRadius = isHovered ? radius + 5 : radius;
    const innerRadius = outerRadius - strokeWidth;

    const startAngleRad = (startAngle - 90) * (Math.PI / 180);
    const endAngleRad = (endAngle - 90) * (Math.PI / 180);

    const x1 = centerX + outerRadius * Math.cos(startAngleRad);
    const y1 = centerY + outerRadius * Math.sin(startAngleRad);
    const x2 = centerX + outerRadius * Math.cos(endAngleRad);
    const y2 = centerY + outerRadius * Math.sin(endAngleRad);

    const x3 = centerX + innerRadius * Math.cos(endAngleRad);
    const y3 = centerY + innerRadius * Math.sin(endAngleRad);
    const x4 = centerX + innerRadius * Math.cos(startAngleRad);
    const y4 = centerY + innerRadius * Math.sin(startAngleRad);

    const largeArcFlag = endAngle - startAngle <= 180 ? "0" : "1";

    return `M ${x1} ${y1} A ${outerRadius} ${outerRadius} 0 ${largeArcFlag} 1 ${x2} ${y2} L ${x3} ${y3} A ${innerRadius} ${innerRadius} 0 ${largeArcFlag} 0 ${x4} ${y4} Z`;
  };

  return (
    <Box sx={{ height: '300px', display: 'flex', flexDirection: 'column', justifyContent: 'center', alignItems: 'center', backgroundColor: 'white', borderRadius: 'var(--radius-md)', position: 'relative' }}>
      {/* Interactive Donut Chart */}
      <svg width="200" height="200" viewBox="0 0 180 180" style={{ marginBottom: '20px' }}>
        {chartData.map((segment, index) => {
          const startAngle = (segment.offset / 100) * 360;
          const endAngle = ((segment.offset + segment.value) / 100) * 360;
          const isHovered = hoveredSegment === segment.name;

          // Calculate the middle angle of the segment for text positioning
          const middleAngle = (startAngle + endAngle) / 2;
          const middleAngleRad = (middleAngle - 90) * (Math.PI / 180);

          // Position text directly on the donut edge/corner
          const textRadius = isHovered ? radius + 2 : radius;
          const textX = 90 + textRadius * Math.cos(middleAngleRad);
          const textY = 90 + textRadius * Math.sin(middleAngleRad);

          return (
            <g key={segment.name}>
              <path
                d={createPath(startAngle, endAngle, isHovered)}
                fill={segment.color}
                style={{
                  cursor: 'pointer',
                  transition: 'all 0.3s ease',
                  filter: isHovered ? 'brightness(1.1)' : 'none',
                  opacity: hoveredSegment && !isHovered ? 0.7 : 1
                }}
                onMouseEnter={() => setHoveredSegment(segment.name)}
                onMouseLeave={() => setHoveredSegment(null)}
              />

              {/* Show value on hovered segment */}
              {isHovered && (
                <text
                  x={textX}
                  y={textY + 1}
                  fontSize="12"
                  fill="black"
                  textAnchor="middle"
                  fontWeight="bold"
                  style={{
                    transition: 'all 0.3s ease',
                    filter: 'drop-shadow(0 1px 2px rgba(255,255,255,0.8))'
                  }}
                >
                  {segment.value}%
                </text>
              )}
            </g>
          );
        })}

        {/* Center circle for donut effect */}
        <circle cx="90" cy="90" r="35" fill="white"/>
        <text x="90" y="85" fontSize="16" fill="#1f2937" textAnchor="middle" fontWeight="bold">100%</text>
        <text x="90" y="100" fontSize="11" fill="#6b7280" textAnchor="middle">Total</text>
      </svg>

      {/* Legend */}
      <Box sx={{
        display: 'grid',
        gridTemplateColumns: 'repeat(2, 1fr)',
        gap: 2,
        width: '100%',
        maxWidth: '300px'
      }}>
        {chartData.map((item) => (
          <Box
            key={item.name}
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              cursor: 'pointer',
              transition: 'all 0.2s ease',
              opacity: hoveredSegment && hoveredSegment !== item.name ? 0.5 : 1,
              transform: hoveredSegment === item.name ? 'scale(1.05)' : 'scale(1)'
            }}
            onMouseEnter={() => setHoveredSegment(item.name)}
            onMouseLeave={() => setHoveredSegment(null)}
          >
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Box sx={{
                width: 12,
                height: 12,
                backgroundColor: item.color,
                borderRadius: '50%',
                transition: 'all 0.2s ease',
                transform: hoveredSegment === item.name ? 'scale(1.2)' : 'scale(1)'
              }} />
              <Typography variant="caption" sx={{ fontSize: '11px', color: '#64748b' }}>
                {item.name}
              </Typography>
            </Box>
            <Box sx={{ textAlign: 'right' }}>
              <Typography variant="caption" sx={{ fontSize: '11px', color: '#64748b', fontWeight: 600 }}>
                {item.value}%
              </Typography>
              <Typography variant="caption" sx={{ fontSize: '10px', color: '#94a3b8', display: 'block' }}>
                {item.actualValue.toLocaleString()}
              </Typography>
            </Box>
          </Box>
        ))}
      </Box>
    </Box>
  );
};

const OverviewTab: React.FC = () => {
  const analyticsMetrics = [
    {
      title: 'Active Users',
      value: '12,847',
      change: '+12.5%',
      changeValue: '+1,432',
      trend: 'up' as const,
      icon: <People />,
      color: 'var(--color-primary-600)',
    },
    {
      title: 'Completion Rate',
      value: '87.3%',
      change: '+3.2%',
      changeValue: '+2.8pp',
      trend: 'up' as const,
      icon: <CheckCircle />,
      color: 'var(--color-success-600)',
    },
    {
      title: 'User Satisfaction',
      value: '4.6',
      change: '+0.2',
      changeValue: 'out of 5.0',
      trend: 'up' as const,
      icon: <Star />,
      color: 'var(--color-warning-600)',
    },
    {
      title: 'Hours Saved',
      value: '2,847',
      change: '+18.7%',
      changeValue: '+447h',
      trend: 'up' as const,
      icon: <Schedule />,
      color: 'var(--color-primary-600)',
    },
  ];

  return (
    <>
        {/* Metrics Overview Cards */}
      <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(4, 1fr)', gap: 2, mb: 3 }}>
        {/* Active Users */}
        <Box sx={{
          p: 3,
          backgroundColor: '#eff6ff',
          borderRadius: '8px',
          border: '1px solid #bfdbfe',
          textAlign: 'left',
          minHeight: '120px',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'space-between',
          cursor: 'pointer',
          transition: 'all 0.2s ease',
          '&:hover': {
            transform: 'translateY(-2px)',
            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)'
          }
        }}>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
            <Typography variant="body2" sx={{ color: '#64748b', fontSize: '13px', fontWeight: 'medium' }}>
              Active Users
            </Typography>
            <Box sx={{
              width: 20,
              height: 20,
              backgroundColor: '#3b82f6',
              borderRadius: '4px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}>
              <Typography sx={{ fontSize: '12px', color: 'white' }}>�</Typography>
            </Box>
          </Box>
          <Typography variant="h3" fontWeight="bold" sx={{ color: '#1e293b', mb: 1, fontSize: '28px' }}>
            34,521
          </Typography>
          <Typography variant="caption" sx={{ color: '#16a34a', fontSize: '11px' }}>
            +2.5% from last month
          </Typography>
        </Box>

        {/* Guide Completion */}
        <Box sx={{
          p: 3,
          backgroundColor: '#f0fdf4',
          borderRadius: '8px',
          border: '1px solid #bbf7d0',
          textAlign: 'left',
          minHeight: '120px',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'space-between',
          cursor: 'pointer',
          transition: 'all 0.2s ease',
          '&:hover': {
            transform: 'translateY(-2px)',
            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)'
          }
        }}>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
            <Typography variant="body2" sx={{ color: '#64748b', fontSize: '13px', fontWeight: 'medium' }}>
              Guide Completion
            </Typography>
            <Box sx={{
              width: 20,
              height: 20,
              backgroundColor: '#22c55e',
              borderRadius: '4px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}>
              <Typography sx={{ fontSize: '12px', color: 'white' }}>✓</Typography>
            </Box>
          </Box>
          <Typography variant="h3" fontWeight="bold" sx={{ color: '#1e293b', mb: 1, fontSize: '28px' }}>
            78.2%
          </Typography>
          <Typography variant="caption" sx={{ color: '#16a34a', fontSize: '11px' }}>
            +3.1% from last month
          </Typography>
        </Box>

        {/* Dona Interactions */}
        <Box sx={{
          p: 3,
          backgroundColor: '#faf5ff',
          borderRadius: '8px',
          border: '1px solid #e9d5ff',
          textAlign: 'left',
          minHeight: '120px',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'space-between',
          cursor: 'pointer',
          transition: 'all 0.2s ease',
          '&:hover': {
            transform: 'translateY(-2px)',
            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)'
          }
        }}>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
            <Typography variant="body2" sx={{ color: '#64748b', fontSize: '13px', fontWeight: 'medium' }}>
              Dona Interactions
            </Typography>
            <Box sx={{
              width: 20,
              height: 20,
              backgroundColor: '#a855f7',
              borderRadius: '4px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}>
              <Typography sx={{ fontSize: '12px', color: 'white' }}>💬</Typography>
            </Box>
          </Box>
          <Typography variant="h3" fontWeight="bold" sx={{ color: '#1e293b', mb: 1, fontSize: '28px' }}>
            12,847
          </Typography>
          <Typography variant="caption" sx={{ color: '#16a34a', fontSize: '11px' }}>
            +18.7% from last month
          </Typography>
        </Box>

        {/* Overall Satisfaction */}
        <Box sx={{
          p: 3,
          backgroundColor: '#fffbeb',
          borderRadius: '8px',
          border: '1px solid #fed7aa',
          textAlign: 'left',
          minHeight: '120px',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'space-between',
          cursor: 'pointer',
          transition: 'all 0.2s ease',
          '&:hover': {
            transform: 'translateY(-2px)',
            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)'
          }
        }}>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
            <Typography variant="body2" sx={{ color: '#64748b', fontSize: '13px', fontWeight: 'medium' }}>
              Overall Satisfaction
            </Typography>
            <Box sx={{
              width: 20,
              height: 20,
              backgroundColor: '#f59e0b',
              borderRadius: '4px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}>
              <Typography sx={{ fontSize: '12px', color: 'white' }}>⭐</Typography>
            </Box>
          </Box>
          <Typography variant="h3" fontWeight="bold" sx={{ color: '#1e293b', mb: 1, fontSize: '28px' }}>
            4.5/5
          </Typography>
          <Typography variant="caption" sx={{ color: '#16a34a', fontSize: '11px' }}>
            +0.2 from last month
          </Typography>
        </Box>
      </Box>

      {/* Overview Charts - Growth Trends and User Satisfaction */}
      <Box sx={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 'var(--spacing-6)', mb: 4 }}>
        {/* User Activity Trends Chart */}
        <Card title="📈 User Activity Trends" subtitle="Active, retained, and total users over time" padding="lg">
          <UserActivityChart />
        </Card>

        {/* Feature Adoption Distribution Chart */}
        <Card title="🎯 Feature Adoption Distribution" subtitle="Interactive feature usage metrics" padding="lg">
          <FeatureAdoptionChart />
        </Card>
      </Box>

      {/* User Feedback & Satisfaction Section */}
      {/* User Satisfaction Ratings and Satisfaction Trend */}
      <Box sx={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 'var(--spacing-6)', mb: 4 }}>
        {/* User Satisfaction Ratings */}
        <Card title="⭐ User Satisfaction Ratings" padding="lg">
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 'var(--spacing-4)' }}>
            {/* Excellent (5★) */}
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, minWidth: 120 }}>
                <Box sx={{ width: 8, height: 8, backgroundColor: '#10b981', borderRadius: '50%' }} />
                <Typography variant="body2" fontWeight="medium">
                  Excellent (5★)
                </Typography>
              </Box>
              <Box sx={{
                flex: 1,
                height: 8,
                backgroundColor: 'var(--color-gray-200)',
                borderRadius: 'var(--radius-full)',
                overflow: 'hidden',
                mr: 2
              }}>
                <Box sx={{
                  width: '65%',
                  height: '100%',
                  backgroundColor: '#10b981',
                  borderRadius: 'var(--radius-full)'
                }} />
              </Box>
              <Typography variant="body2" fontWeight="bold" sx={{ minWidth: 40, textAlign: 'right' }}>
                1,247
              </Typography>
            </Box>

            {/* Good (4★) */}
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, minWidth: 120 }}>
                <Box sx={{ width: 8, height: 8, backgroundColor: '#84cc16', borderRadius: '50%' }} />
                <Typography variant="body2" fontWeight="medium">
                  Good (4★)
                </Typography>
              </Box>
              <Box sx={{
                flex: 1,
                height: 8,
                backgroundColor: 'var(--color-gray-200)',
                borderRadius: 'var(--radius-full)',
                overflow: 'hidden',
                mr: 2
              }}>
                <Box sx={{
                  width: '45%',
                  height: '100%',
                  backgroundColor: '#84cc16',
                  borderRadius: 'var(--radius-full)'
                }} />
              </Box>
              <Typography variant="body2" fontWeight="bold" sx={{ minWidth: 40, textAlign: 'right' }}>
                892
              </Typography>
            </Box>

            {/* Average (3★) */}
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, minWidth: 120 }}>
                <Box sx={{ width: 8, height: 8, backgroundColor: '#f59e0b', borderRadius: '50%' }} />
                <Typography variant="body2" fontWeight="medium">
                  Average (3★)
                </Typography>
              </Box>
              <Box sx={{
                flex: 1,
                height: 8,
                backgroundColor: 'var(--color-gray-200)',
                borderRadius: 'var(--radius-full)',
                overflow: 'hidden',
                mr: 2
              }}>
                <Box sx={{
                  width: '22%',
                  height: '100%',
                  backgroundColor: '#f59e0b',
                  borderRadius: 'var(--radius-full)'
                }} />
              </Box>
              <Typography variant="body2" fontWeight="bold" sx={{ minWidth: 40, textAlign: 'right' }}>
                434
              </Typography>
            </Box>

            {/* Poor (2★) */}
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, minWidth: 120 }}>
                <Box sx={{ width: 8, height: 8, backgroundColor: '#f97316', borderRadius: '50%' }} />
                <Typography variant="body2" fontWeight="medium">
                  Poor (2★)
                </Typography>
              </Box>
              <Box sx={{
                flex: 1,
                height: 8,
                backgroundColor: 'var(--color-gray-200)',
                borderRadius: 'var(--radius-full)',
                overflow: 'hidden',
                mr: 2
              }}>
                <Box sx={{
                  width: '8%',
                  height: '100%',
                  backgroundColor: '#f97316',
                  borderRadius: 'var(--radius-full)'
                }} />
              </Box>
              <Typography variant="body2" fontWeight="bold" sx={{ minWidth: 40, textAlign: 'right' }}>
                123
              </Typography>
            </Box>

            {/* Very Poor (1★) */}
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, minWidth: 120 }}>
                <Box sx={{ width: 8, height: 8, backgroundColor: '#ef4444', borderRadius: '50%' }} />
                <Typography variant="body2" fontWeight="medium">
                  Very Poor (1★)
                </Typography>
              </Box>
              <Box sx={{
                flex: 1,
                height: 8,
                backgroundColor: 'var(--color-gray-200)',
                borderRadius: 'var(--radius-full)',
                overflow: 'hidden',
                mr: 2
              }}>
                <Box sx={{
                  width: '4%',
                  height: '100%',
                  backgroundColor: '#ef4444',
                  borderRadius: 'var(--radius-full)'
                }} />
              </Box>
              <Typography variant="body2" fontWeight="bold" sx={{ minWidth: 40, textAlign: 'right' }}>
                57
              </Typography>
            </Box>

            {/* Summary Cards */}
            <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(3, 1fr)', gap: 2, mt: 3 }}>
              <Box sx={{
                p: 2,
                backgroundColor: '#f0fdf4',
                borderRadius: 'var(--radius-md)',
                textAlign: 'center'
              }}>
                <Typography variant="h5" fontWeight="bold" color="#16a34a">
                  77%
                </Typography>
                <Typography variant="caption" color="#16a34a">
                  Positive
                </Typography>
              </Box>
              <Box sx={{
                p: 2,
                backgroundColor: '#fffbeb',
                borderRadius: 'var(--radius-md)',
                textAlign: 'center'
              }}>
                <Typography variant="h5" fontWeight="bold" color="#d97706">
                  16%
                </Typography>
                <Typography variant="caption" color="#d97706">
                  Neutral
                </Typography>
              </Box>
              <Box sx={{
                p: 2,
                backgroundColor: '#fef2f2',
                borderRadius: 'var(--radius-md)',
                textAlign: 'center'
              }}>
                <Typography variant="h5" fontWeight="bold" color="#dc2626">
                  7%
                </Typography>
                <Typography variant="caption" color="#dc2626">
                  Negative
                </Typography>
              </Box>
            </Box>
          </Box>
        </Card>

        {/* Satisfaction Trend */}
        <Card title="📈 Satisfaction Trend" padding="lg">
          <Box sx={{ height: '300px', display: 'flex', flexDirection: 'column', justifyContent: 'center', alignItems: 'center', backgroundColor: '#f8fafc', borderRadius: 'var(--radius-md)', position: 'relative' }}>
            {/* Satisfaction Trend Line Chart */}
            <svg width="100%" height="250" viewBox="0 0 400 200" style={{ overflow: 'visible' }}>
              {/* Grid lines */}
              <defs>
                <pattern id="feedbackGrid" width="66.67" height="40" patternUnits="userSpaceOnUse">
                  <path d="M 66.67 0 L 0 0 0 40" fill="none" stroke="#e2e8f0" strokeWidth="0.5"/>
                </pattern>
              </defs>
              <rect width="100%" height="100%" fill="url(#feedbackGrid)" />

              {/* Y-axis labels */}
              <text x="10" y="20" fontSize="10" fill="#64748b">5</text>
              <text x="10" y="60" fontSize="10" fill="#64748b">4.75</text>
              <text x="10" y="100" fontSize="10" fill="#64748b">4.5</text>
              <text x="10" y="140" fontSize="10" fill="#64748b">4.25</text>
              <text x="10" y="180" fontSize="10" fill="#64748b">4</text>

              {/* X-axis labels */}
              <text x="50" y="215" fontSize="10" fill="#64748b">Jan</text>
              <text x="110" y="215" fontSize="10" fill="#64748b">Feb</text>
              <text x="170" y="215" fontSize="10" fill="#64748b">Mar</text>
              <text x="230" y="215" fontSize="10" fill="#64748b">Apr</text>
              <text x="290" y="215" fontSize="10" fill="#64748b">May</text>
              <text x="350" y="215" fontSize="10" fill="#64748b">Jun</text>

              {/* Line showing satisfaction trend from 4.2 to 4.7 */}
              <path d="M 50 168 L 110 152 L 170 136 L 230 120 L 290 104 L 350 88" fill="none" stroke="#3b82f6" strokeWidth="3"/>

              {/* Data points */}
              <circle cx="50" cy="168" r="4" fill="#3b82f6"/>
              <circle cx="110" cy="152" r="4" fill="#3b82f6"/>
              <circle cx="170" cy="136" r="4" fill="#3b82f6"/>
              <circle cx="230" cy="120" r="4" fill="#3b82f6"/>
              <circle cx="290" cy="104" r="4" fill="#3b82f6"/>
              <circle cx="350" cy="88" r="4" fill="#3b82f6"/>

              {/* Value labels on data points */}
              <text x="50" y="160" fontSize="9" fill="#3b82f6" textAnchor="middle">4.2</text>
              <text x="110" y="144" fontSize="9" fill="#3b82f6" textAnchor="middle">4.3</text>
              <text x="170" y="128" fontSize="9" fill="#3b82f6" textAnchor="middle">4.4</text>
              <text x="230" y="112" fontSize="9" fill="#3b82f6" textAnchor="middle">4.5</text>
              <text x="290" y="96" fontSize="9" fill="#3b82f6" textAnchor="middle">4.6</text>
              <text x="350" y="80" fontSize="9" fill="#3b82f6" textAnchor="middle">4.7</text>
            </svg>
          </Box>
        </Card>
      </Box>

    
      {/* <Card title="📊 Feedback Summary" padding="lg">
        <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(4, 1fr)', gap: 'var(--spacing-6)' }}>
          
          <Box sx={{
            p: 3,
            backgroundColor: '#f8fafc',
            borderRadius: 'var(--radius-md)',
            textAlign: 'center'
          }}>
            <Typography variant="h3" fontWeight="bold" color="text.primary" sx={{ mb: 1 }}>
              2,238
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Total Feedback
            </Typography>
          </Box>

        
          <Box sx={{
            p: 3,
            backgroundColor: '#f0fdf4',
            borderRadius: 'var(--radius-md)',
            textAlign: 'center'
          }}>
            <Typography variant="h3" fontWeight="bold" color="#16a34a" sx={{ mb: 1 }}>
              85.8%
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Positive Sentiment
            </Typography>
          </Box>

       
          <Box sx={{
            p: 3,
            backgroundColor: '#eff6ff',
            borderRadius: 'var(--radius-md)',
            textAlign: 'center'
          }}>
            <Typography variant="h3" fontWeight="bold" color="#2563eb" sx={{ mb: 1 }}>
              4.6/5
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Average Rating
            </Typography>
          </Box>

         
          <Box sx={{
            p: 3,
            backgroundColor: '#fdf4ff',
            borderRadius: 'var(--radius-md)',
            textAlign: 'center'
          }}>
            <Typography variant="h3" fontWeight="bold" color="#9333ea" sx={{ mb: 1 }}>
              +12%
            </Typography>
            <Typography variant="body2" color="text.secondary">
              vs Last Month
            </Typography>
          </Box>
        </Box>
      </Card> */}

    

    
      {/* <Box sx={{
        p: 4,
        backgroundColor: '#f8fafc',
        borderRadius: '8px',
        border: '1px solid #e2e8f0'
      }}>
        <Typography variant="h6" fontWeight="bold" sx={{
          color: '#1e293b',
          textAlign: 'center',
          mb: 4,
          fontSize: '16px'
        }}>
          Agent Collaboration Flow
        </Typography>

        <Box sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          gap: 4,
          mb: 3
        }}>
          <Box sx={{ textAlign: 'center', minWidth: '100px' }}>
            <Box sx={{
              width: 48,
              height: 48,
              backgroundColor: '#a855f7',
              borderRadius: '8px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              mx: 'auto',
              mb: 2
            }}>
              <Typography sx={{ fontSize: '20px', color: 'white' }}>💬</Typography>
            </Box>
            <Typography variant="body1" fontWeight="bold" sx={{ color: '#1e293b', mb: 0.5, fontSize: '14px' }}>
              Dona
            </Typography>
            <Typography variant="body2" sx={{ color: '#64748b', mb: 0.5, fontSize: '12px' }}>
              Answers Questions
            </Typography>
            <Typography variant="caption" sx={{
              color: '#7c3aed',
              fontWeight: 'medium',
              fontSize: '11px'
            }}>
              8,136 responses
            </Typography>
          </Box>

         
          <Box sx={{
            color: '#94a3b8',
            fontSize: '20px',
            display: 'flex',
            alignItems: 'center',
            mt: -1
          }}>
            →
          </Box>

        
          <Box sx={{ textAlign: 'center', minWidth: '100px' }}>
            <Box sx={{
              width: 48,
              height: 48,
              backgroundColor: '#22c55e',
              borderRadius: '8px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              mx: 'auto',
              mb: 2
            }}>
              <Typography sx={{ fontSize: '20px', color: 'white' }}>👁️</Typography>
            </Box>
            <Typography variant="body1" fontWeight="bold" sx={{ color: '#1e293b', mb: 0.5, fontSize: '14px' }}>
              Rookie
            </Typography>
            <Typography variant="body2" sx={{ color: '#64748b', mb: 0.5, fontSize: '12px' }}>
              Creates Tours
            </Typography>
            <Typography variant="caption" sx={{
              color: '#16a34a',
              fontWeight: 'medium',
              fontSize: '11px'
            }}>
              1,360 tours
            </Typography>
          </Box>

       
          <Box sx={{
            color: '#94a3b8',
            fontSize: '20px',
            display: 'flex',
            alignItems: 'center',
            mt: -1
          }}>
            →
          </Box>

          
          <Box sx={{ textAlign: 'center', minWidth: '100px' }}>
            <Box sx={{
              width: 48,
              height: 48,
              backgroundColor: '#3b82f6',
              borderRadius: '8px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              mx: 'auto',
              mb: 2
            }}>
              <Typography sx={{ fontSize: '20px', color: 'white' }}>⚙️</Typography>
            </Box>
            <Typography variant="body1" fontWeight="bold" sx={{ color: '#1e293b', mb: 0.5, fontSize: '14px' }}>
              Work Agent
            </Typography>
            <Typography variant="body2" sx={{ color: '#64748b', mb: 0.5, fontSize: '12px' }}>
              Automates Rookie
            </Typography>
            <Typography variant="caption" sx={{
              color: '#2563eb',
              fontWeight: 'medium',
              fontSize: '11px'
            }}>
              711 automations
            </Typography>
          </Box>
        </Box>

      
        <Typography variant="body2" sx={{
          color: '#64748b',
          textAlign: 'center',
          fontSize: '12px',
          maxWidth: '500px',
          mx: 'auto'
        }}>
          Work Agent optimizes Rookie's tour creation process, improving efficiency by 50%
        </Typography>
      </Box> */}
    </>
  );
};

export default OverviewTab;
