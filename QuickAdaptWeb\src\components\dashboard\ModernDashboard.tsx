import React, { useState, Suspense } from 'react';
import { Box, Typography, Container, Tabs, Tab, CircularProgress, Button } from '@mui/material';
import { styled } from '@mui/material/styles';
import {
  FilterList,
  CalendarToday,
  <PERSON><PERSON><PERSON>,
  TrendingUp,
  SmartToy
} from '@mui/icons-material';

// Lazy load tab components
const OverviewTab = React.lazy(() => import('./OverviewTab'));
const AnalyticsTab = React.lazy(() => import('./AnalyticsTab'));
const AIPerformanceTab = React.lazy(() => import('./AIPerformanceTab'));

const DashboardContainer = styled(Box)({
  backgroundColor: '#f8fafc',
  minHeight: '100vh',
});

const TabsContainer = styled(Box)({
  marginBottom: '24px',
  backgroundColor: 'white',
  borderRadius: '12px',
  boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)',
  padding: '8px',
  '& .MuiTabs-root': {
    minHeight: '48px',
    '& .MuiTabs-indicator': {
      display: 'none', // Hide the default indicator
    },
  },
  '& .MuiTab-root': {
    textTransform: 'none',
    fontWeight: 500,
    fontSize: '14px',
    minHeight: '40px',
    borderRadius: '8px',
    margin: '0 4px',
    color: '#64748b',
    transition: 'all 0.2s ease-in-out',
    '& .MuiSvgIcon-root': {
      fontSize: '16px',
      marginRight: '6px',
    },
    '&.Mui-selected': {
      backgroundColor: '#3b82f6',
      color: 'white',
      fontWeight: 600,
    },
    '&:hover:not(.Mui-selected)': {
      backgroundColor: '#f1f5f9',
      color: '#475569',
    },
  },
});

const TabLabel = styled(Box)({
  display: 'flex',
  alignItems: 'center',
  gap: '6px',
});

const FilterButton = styled(Button)({
  display: 'flex',
  alignItems: 'center',
  gap: '8px',
  minWidth: '140px',
  justifyContent: 'space-between',
  padding: '8px 12px',
  backgroundColor: 'transparent',
  color: '#3b82f6',
  border: '1px solid #3b82f6',
  borderRadius: '8px',
  textTransform: 'none',
  fontSize: '14px',
  fontWeight: 'medium',
  '&:hover': {
    backgroundColor: '#eff6ff',
    borderColor: '#2563eb',
    color: '#2563eb',
  },
});

const ModernDashboard: React.FC = () => {
  const [selectedTab, setSelectedTab] = useState(0);
  const [timeFilter, setTimeFilter] = useState('7d');
  const [timeFilterOpen, setTimeFilterOpen] = useState(false);

  const timeFilterOptions = [
    { label: 'Last 7 days', value: '7d' },
    { label: 'Last 30 days', value: '30d' },
    { label: 'Last 90 days', value: '90d' },
    { label: 'Last year', value: '1y' },
  ];

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setSelectedTab(newValue);
  };

  const renderTabContent = () => {
    const LoadingSpinner = () => (
      <Box sx={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '400px',
        flexDirection: 'column',
        gap: 2
      }}>
        <CircularProgress size={40} />
        <Typography variant="body2" color="text.secondary">
          Loading dashboard content...
        </Typography>
      </Box>
    );

    switch (selectedTab) {
      case 0: // Overview
        return (
          <Suspense fallback={<LoadingSpinner />}>
            <OverviewTab />
          </Suspense>
        );

      case 1: // Analytics
        return (
          <Suspense fallback={<LoadingSpinner />}>
            <AnalyticsTab />
          </Suspense>
        );

      case 2: // AI Performance
        return (
          <Suspense fallback={<LoadingSpinner />}>
            <AIPerformanceTab />
          </Suspense>
        );

      default:
        return null;
    }
  };

  return (
    <div className='qadpt-web'>
      <div className='qadpt-webcontent'>
        <DashboardContainer>
          <Container maxWidth="xl" sx={{ py: 3 }}>
            {/* Header Section */}
            <Box sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              mb: 4,
              pb: 3,
              borderBottom: '1px solid #e2e8f0'
            }}>
              {/* Left Side - Title and Subtitle */}
              <Box>
                <Typography variant="h4" fontWeight="bold" sx={{ color: '#1e293b', mb: 0.5 }}>
                  Digital Adoption Platform
                </Typography>
                <Typography variant="body2" sx={{ color: '#64748b' }}>
                  Admin Dashboard - Guide Creation & Analytics
                </Typography>
              </Box>

              {/* Right Side - Time Filter */}
              <Box sx={{ position: 'relative' }}>
                <FilterButton
                  onClick={() => setTimeFilterOpen(!timeFilterOpen)}
                >
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <CalendarToday sx={{ fontSize: '16px' }} />
                    <Typography variant="body2">
                      {timeFilterOptions.find(opt => opt.value === timeFilter)?.label}
                    </Typography>
                  </Box>
                  <FilterList sx={{ fontSize: '16px' }} />
                </FilterButton>

                {/* Dropdown Menu */}
                {timeFilterOpen && (
                  <Box sx={{
                    position: 'absolute',
                    top: '100%',
                    right: 0,
                    mt: 1,
                    minWidth: '200px',
                    backgroundColor: 'white',
                    borderRadius: 'var(--radius-md)',
                    border: '1px solid #e2e8f0',
                    boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
                    zIndex: 1000
                  }}>
                    {timeFilterOptions.map((option, index) => {
                      const isSelected = timeFilter === option.value;
                      return (
                        <Box
                          key={option.value}
                          onClick={(e) => {
                            e.stopPropagation();
                            setTimeFilter(option.value);
                            setTimeFilterOpen(false);
                          }}
                          sx={{
                            p: 2,
                            cursor: 'pointer',
                            backgroundColor: isSelected ? '#f1f5f9' : 'transparent',
                            color: isSelected ? '#1e293b' : '#64748b',
                            fontWeight: isSelected ? 'medium' : 'normal',
                            borderRadius: index === 0 ? 'var(--radius-md) var(--radius-md) 0 0' :
                                         index === timeFilterOptions.length - 1 ? '0 0 var(--radius-md) var(--radius-md)' : '0',
                            '&:hover': {
                              backgroundColor: isSelected ? '#f1f5f9' : '#f8fafc'
                            }
                          }}
                        >
                          <Typography variant="body2">
                            {option.label}
                          </Typography>
                        </Box>
                      );
                    })}
                  </Box>
                )}
              </Box>
            </Box>

            {/* Tabs Navigation */}
            <TabsContainer>
              <Tabs
                value={selectedTab}
                onChange={handleTabChange}
                variant="fullWidth"
                TabIndicatorProps={{ style: { display: 'none' } }}
              >
                <Tab 
                  label={
                    <TabLabel>
                      <BarChart />
                      Overview
                    </TabLabel>
                  } 
                />
                <Tab 
                  label={
                    <TabLabel>
                      <TrendingUp />
                      Analytics
                    </TabLabel>
                  } 
                />
                <Tab 
                  label={
                    <TabLabel>
                      <SmartToy />
                      AI Agents
                    </TabLabel>
                  } 
                />
              </Tabs>
            </TabsContainer>

            {/* Tab Content */}
            <Box>
              {renderTabContent()}
            </Box>
          </Container>
        </DashboardContainer>
      </div>
    </div>
  );
};

export default ModernDashboard;