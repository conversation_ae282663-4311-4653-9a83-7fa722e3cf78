import React, { useState, useEffect } from "react";
import { fetchOrganizations, getOrganizations, updateOrganization } from "../../services/OrganizationService";
import { adminUrl } from "../../services/APIService";
import { timezones } from "../../timezones";
import { Alert, FormControlLabel, Snackbar, Switch } from '@mui/material';

const EditOrganization = (props: any) => {
  const {
    modelsData,
    setModelsData,
    showEditPopup,
    setShowEditPopup,
    OrganizationId,
    sortModel,
    filters,
    setLoading,
    setModels,
    models,
    handleClose,
    skip,
    top,
    setTotalcount,
    updateOrganizationDetails
  } = props;

  const [userDetails, setUserDetails] = useState({
    OrganizationId: "",
    Name: "",
    TimeZone: "Asia/Kolkata", // default value
    DateFormat: "dd-MM-yyyy",
    Logo: "",
    Status: "",
    CreatedDate: "",
    UpdatedDate: "",
    AuthorizationType: "",
    IsActive: "",
    Rtl: false,
    TimeFormat: "",
    ThemeId: "",
    Type: "",
    OrganizationPlanId: "",
    OrganizationPlan: null,
    Plan: "",
  });

  const [errors, setErrors] = useState({
    Name: "",
    Logo: "",
    TimeZone: "",
    DateFormat: "",
    Type: "",
    Rtl: ""
  });
  const [initialValues, setInitialValues] = useState({ ...userDetails });

  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState("");
  const [snackbarSeverity, setSnackbarSeverity] = useState<"success" | "error">("success");

  const handleTimezoneChange = (event: any) => {
    setUserDetails(prevDetails => ({ ...prevDetails, TimeZone: event.target.value }));
  };

  const handleFileChange = (event: any) => {
    if (event.target.files && event.target.files[0]) {
      const file = event.target.files[0];
      setUserDetails(prevDetails => ({ ...prevDetails, Logo: file.name }));
    }
  };

  const handleSnackbarClose = () => {
    setSnackbarOpen(false);
  };

  useEffect(() => {
    const checkNameUniqueness = async () => {
      if (userDetails.Name.trim() === "") {
        setErrors(prevErrors => ({
          ...prevErrors,
          Name: ""
        }));
        return;
      }

      const isNameUnique = modelsData.every((org: { Name: string; OrganizationId: string }) => 
        org.Name !== userDetails.Name || org.OrganizationId === userDetails.OrganizationId
      );

      if (!isNameUnique) {
        setErrors(prevErrors => ({
          ...prevErrors,
          Name: 'Organization Name already exists'
        }));
      } else {
        setErrors(prevErrors => ({
          ...prevErrors,
          Name: ""
        }));
      }
    };

    if (userDetails.Name !== "") {
      checkNameUniqueness();
    }
  }, [userDetails.Name, modelsData, models, userDetails.OrganizationId]);

  const handleChange = (e: any) => {
    const { name, value } = e.target;
    
    setUserDetails(prevDetails => ({
      ...prevDetails,
      [name]: value,
    }));

    // Perform validation on change
    validateField(name, value);
  };

  const handleBlur = (e: any) => {
    const { name, value } = e.target;
    validateField(name, value);
  };

  const validateField = (name: string, value: any) => {
    let isValid = true;
    let newErrors: any = { ...errors };

    switch (name) {
      case "Name":
        if (!value) {
          newErrors.Name = "Organization Name is required";
          isValid = false;
        } else if (value.length < 5) {
          newErrors.Name = "Organization Name must be at least 5 characters";
          isValid = false;
        } else if (value.length > 50) {
          newErrors.Name = "Organization Name must be at most 50 characters";
          isValid = false;
        } else if (!/^[a-zA-Z0-9 ]+$/.test(value)) {
          newErrors.Name = "Organization Name can only contain letters, numbers, and spaces";
          isValid = false;
        } else if (!checkNameUniqueness(value, userDetails.OrganizationId)) {
          newErrors.Name = 'Organization Name already exists';
          isValid = false;
        } else {
          newErrors.Name = "";
        }
        break;
      case "Logo":
        if (!value) {
          newErrors.Logo = "Logo is required";
          isValid = false;
        } else {
          newErrors.Logo = "";
        }
        break;
      case "TimeZone":
        if (!value) {
          newErrors.TimeZone = "Timezone is required";
          isValid = false;
        } else {
          newErrors.TimeZone = "";
        }
        break;
      case "DateFormat":
        if (!value) {
          newErrors.DateFormat = "Date Format is required";
          isValid = false;
        } else {
          newErrors.DateFormat = "";
        }
        break;
      case "Type":
        if (!value) {
          newErrors.Type = "Type is required";
          isValid = false;
        } else {
          newErrors.Type = "";
        }
        break;
      default:
        break;
    }

    setErrors(newErrors);
    return isValid;
  };

  const validateFields = () => {
    let isValid = true;

    for (const [name, value] of Object.entries(userDetails)) {
      if (!validateField(name, value)) {
        isValid = false;
      }
    }

    return isValid;
  };

  const checkNameUniqueness = (name: string, currentOrgId: string) => {
    const isNameUnique = modelsData.every((org: { Name: string; OrganizationId: string }) => 
      org.Name !== name || org.OrganizationId === currentOrgId
    );
    return isNameUnique;
  };

  const isFormChanged = () => {
    return JSON.stringify(userDetails) !== JSON.stringify(initialValues);
  };

  const handleSubmit = async (e: any) => {
    e.preventDefault();

    if (validateFields()) {
      try {
        updateOrganizationDetails(userDetails);
      } catch (error) {
        console.error("Failed to update organization:", error);
        setSnackbarMessage("Failed to update organization");
        setSnackbarSeverity("error");
        setSnackbarOpen(true);
      }
    } else {
      setSnackbarMessage("Please correct the errors in the form");
      setSnackbarSeverity("error");
      setSnackbarOpen(true);
    }
  };

  useEffect(() => {
    if (showEditPopup) {
      fetchOrganizationDetails(OrganizationId);
    }
  }, [showEditPopup, OrganizationId]);

  const fetchOrganizationDetails = async (id: any) => {
    try {
      const token = localStorage.getItem('access_token'); 
      const response = await fetch(`${adminUrl}/Organization/GetOrganizationById?organizationId=${id}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`, 
          'Content-Type': 'application/json' 
        }
      });
      if (!response.ok) {
        throw new Error("Network response was not ok");
      }
      const data = await response.json();
      setUserDetails({
        OrganizationId: data.OrganizationId,
        Name: data.Name,
        TimeZone: data.TimeZone,
        DateFormat: data.DateFormat,
        Logo: data.Logo,
        Status: data.Status,
        CreatedDate: data.CreatedDate,
        UpdatedDate: data.UpdatedDate,
        AuthorizationType: data.AuthorizationType,
        IsActive: data.IsActive,
        Rtl: data.RTL,
        TimeFormat: data.TimeFormat,
        ThemeId: data.ThemeId,
        Type: data.Type,
        OrganizationPlanId: data.OrganizationPlanId,
        OrganizationPlan: data.OrganizationPlan,
        Plan: data.Plan
      });
      setInitialValues({
        OrganizationId: data.OrganizationId,
        Name: data.Name,
        TimeZone: data.TimeZone,
        DateFormat: data.DateFormat,
        Logo: data.Logo,
        Status: data.Status,
        CreatedDate: data.CreatedDate,
        UpdatedDate: data.UpdatedDate,
        AuthorizationType: data.AuthorizationType,
        IsActive: data.IsActive,
        Rtl: data.RTL,
        TimeFormat: data.TimeFormat,
        ThemeId: data.ThemeId,
        Type: data.Type,
        OrganizationPlanId: data.OrganizationPlanId,
        OrganizationPlan: data.OrganizationPlan,
        Plan: data.Plan
      });
    } catch (error) {
      console.error("Failed to fetch organization details:", error);
    }
  };

  return (
    <div>
    {showEditPopup && (
      <div className="user-popup">
        <div className="qadpt-header">
          <span>Edit Organization</span>
          <svg
            onClick={() => handleClose()}
            className="close-icon"
            xmlns="http://www.w3.org/2000/svg"
            x="0px"
            y="0px"
            width="24"
            height="24"
            viewBox="0 0 50 50"
          >
            <path d="M 7.71875 6.28125 L 6.28125 7.71875 L 23.5625 25 L 6.28125 42.28125 L 7.71875 43.71875 L 25 26.4375 L 42.28125 43.71875 L 43.71875 42.28125 L 26.4375 25 L 43.71875 7.71875 L 42.28125 6.28125 L 25 23.5625 Z"></path>
          </svg>
        </div>
        <div className="qadpt-usrform">
          <form onSubmit={handleSubmit}>
            <div className="qadpt-txtfld">
              <label htmlFor="organizationname" className="qadpt-txtlabel">Organization Name*</label>
              <input
                className="qadpt-txtinp"
                type="text"
                name="Name"
                value={userDetails.Name}
                onChange={handleChange}
              />
               {errors.Name && <div className="error">{errors.Name}</div>}
            </div>
    
            {/* <div className="qadpt-txtfld">
              <label htmlFor="logo" className="qadpt-txtlabel">Logo*</label>
              <input
                className="qadpt-txtinp"
                type="file"
                name="logo"
                accept="image/*"
                onChange={handleFileChange}
              />
              {errors.Logo && <div className="error">{errors.Logo}</div>}
            </div> */}
    
            {/* <div className="qadpt-txtfld">
              <label htmlFor="timezone" className="qadpt-txtlabel">Timezone*</label>
              <select
                 value={userDetails.TimeZone}
                onChange={handleTimezoneChange}
                className="qadpt-txtinp"
              >
                {timezones.map((timezone) => (
                  <option key={timezone.Id} value={timezone.Id}>
                    {timezone.DisplayName}
                  </option>
                ))}
              </select>
              {errors.TimeZone && <span className="error">{errors.TimeZone}</span>}
            </div> */}
    
            {/* <div className="qadpt-txtfld">
              <label htmlFor="dateFormat" className="qadpt-txtlabel">Date Format*</label>
              <select
                id="dateFormat"
                name="DateFormat"
                value={userDetails.DateFormat}
                onChange={handleChange}
                className={errors.DateFormat ? "error-input" : "qadpt-txtinp"}
              >
                <option value="dd-MM-yyyy">dd-MM-yyyy</option>
                <option value="MM-dd-yyyy">MM-dd-yyyy</option>
                <option value="yyyy-MM-dd">yyyy-MM-dd</option>
              </select>
              {errors.DateFormat && <span className="error">{errors.DateFormat}</span>}            </div> */}
    
            <div className="qadpt-txtfld">
              <label htmlFor="type" className="qadpt-txtlabel">Type*</label>
              <select
                id="type"
                name="Type"
                value={userDetails.Type}
                onChange={handleChange}
                className={errors.Type ? "error-input" : "qadpt-txtinp"}
              >
                <option value="Client">Client</option>
                <option value="Testing">Testing</option>
                <option value="POC">POC</option>
                <option value="Prospects">Prospects</option>
              </select>
                {errors.Type && <span className="error">{errors.Type}</span>}
              </div>
    
            <div className="qadpt-txtfld qadpt-switch">
              <span className="qadpt-txtlabel">RTL</span>
             
              <FormControlLabel
					control={

						<Switch
								
      checked={userDetails.Rtl} // Reflects the state correctly
      onChange={(e) => setUserDetails(prevDetails => ({
        ...prevDetails,
        Rtl: e.target.checked
      }))}
    />
    }label=""
                />
             





            </div>
          </form>
        </div>
        <div className="qadpt-button">
        <button 
              type="submit" 
            className={isFormChanged() ? 'qadpt-enab' : 'qadpt-disab'}
            disabled={!isFormChanged()}
		onClick={handleSubmit}
            >
              Update
            </button>
            </div>
      </div>
  
    )}
  <Snackbar
  open={snackbarOpen}
  autoHideDuration={6000}
  onClose={handleSnackbarClose}
  anchorOrigin={{ vertical: "top", horizontal: "center" }}
  sx={{ zIndex: 10000 ,marginTop:4}} // Optionally adjust the zIndex if needed
>
  <Alert
      onClose={handleSnackbarClose}
      severity={snackbarSeverity}
      sx={{ width: "100%" }}
  >
      {snackbarMessage}
  </Alert>
</Snackbar>    </div>
  );
};

export default EditOrganization;
